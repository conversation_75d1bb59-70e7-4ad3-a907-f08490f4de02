/**
 * Manual test script for Recipe View Statistics APIs
 * 
 * This script tests both new APIs:
 * 1. GET /api/v1/private/analytics/recipe-view-statistics/:recipeId
 * 2. DELETE /api/v1/private/analytics/recipe-view-statistics/:recipeId/reset
 * 
 * Prerequisites:
 * - Server must be running
 * - Database must have test data with:
 *   - A private recipe with assigned users
 *   - Some analytics data for recipe views
 * 
 * Usage:
 * ts-node tests/manual/recipe-view-statistics-test.ts
 */

import axios from 'axios';

// Configuration
const BASE_URL = 'http://localhost:3000/api/v1/private/analytics';
const TEST_RECIPE_ID = 1; // Replace with actual private recipe ID that has assigned users
const TEST_USER_IDS = [1, 2]; // Replace with actual user IDs assigned to the recipe
const AUTH_TOKEN = 'your-jwt-token-here'; // Replace with valid JWT token

// Test data setup
const config = {
  headers: {
    'Authorization': `Bearer ${AUTH_TOKEN}`,
    'Content-Type': 'application/json'
  }
};

async function testGetRecipeViewStatistics() {
  console.log('\n=== Testing GET Recipe View Statistics ===');
  
  try {
    const response = await axios.get(
      `${BASE_URL}/recipe-view-statistics/${TEST_RECIPE_ID}`,
      config
    );
    
    console.log('✅ GET Request successful');
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
    // Validate response structure
    const { status, message, data } = response.data;
    
    if (status && data) {
      console.log('✅ Response structure is valid');
      
      // Check required fields
      const requiredFields = ['recipe_id', 'recipe_title', 'assigned_users_count', 'statistics', 'user_views'];
      const missingFields = requiredFields.filter(field => !(field in data));
      
      if (missingFields.length === 0) {
        console.log('✅ All required fields present');
      } else {
        console.log('❌ Missing fields:', missingFields);
      }
      
      // Check statistics structure
      if (data.statistics && typeof data.statistics === 'object') {
        console.log('✅ Statistics object present');
        console.log('  - Unique viewers:', data.statistics.unique_viewers);
        console.log('  - Total views:', data.statistics.total_views);
      }
      
      // Check user_views array
      if (Array.isArray(data.user_views)) {
        console.log('✅ User views array present with', data.user_views.length, 'entries');
        if (data.user_views.length > 0) {
          console.log('  - Sample user view:', data.user_views[0]);
        }
      }
    } else {
      console.log('❌ Invalid response structure');
    }
    
  } catch (error: any) {
    console.log('❌ GET Request failed');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data);
    } else {
      console.log('Error:', error.message);
    }
  }
}

async function testResetRecipeViewStatisticsSpecificUsers() {
  console.log('\n=== Testing DELETE Recipe View Statistics (Specific Users) ===');
  
  try {
    const response = await axios.delete(
      `${BASE_URL}/recipe-view-statistics/${TEST_RECIPE_ID}/reset`,
      {
        ...config,
        data: {
          user_ids: TEST_USER_IDS
        }
      }
    );
    
    console.log('✅ DELETE Request (specific users) successful');
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
    // Validate response structure
    const { status, message } = response.data;
    
    if (status) {
      console.log('✅ Reset successful for specific users');
    } else {
      console.log('❌ Reset failed:', message);
    }
    
  } catch (error: any) {
    console.log('❌ DELETE Request (specific users) failed');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data);
    } else {
      console.log('Error:', error.message);
    }
  }
}

async function testResetRecipeViewStatisticsAllUsers() {
  console.log('\n=== Testing DELETE Recipe View Statistics (All Users) ===');
  
  try {
    const response = await axios.delete(
      `${BASE_URL}/recipe-view-statistics/${TEST_RECIPE_ID}/reset`,
      config
    );
    
    console.log('✅ DELETE Request (all users) successful');
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
    // Validate response structure
    const { status, message } = response.data;
    
    if (status) {
      console.log('✅ Reset successful for all users');
    } else {
      console.log('❌ Reset failed:', message);
    }
    
  } catch (error: any) {
    console.log('❌ DELETE Request (all users) failed');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data);
    } else {
      console.log('Error:', error.message);
    }
  }
}

async function testErrorCases() {
  console.log('\n=== Testing Error Cases ===');
  
  // Test with invalid recipe ID
  try {
    await axios.get(`${BASE_URL}/recipe-view-statistics/999999`, config);
    console.log('❌ Should have failed for non-existent recipe');
  } catch (error: any) {
    if (error.response && error.response.status === 400) {
      console.log('✅ Correctly handled non-existent recipe');
    } else {
      console.log('❌ Unexpected error for non-existent recipe:', error.response?.data);
    }
  }
  
  // Test with invalid user IDs in reset
  try {
    await axios.delete(
      `${BASE_URL}/recipe-view-statistics/${TEST_RECIPE_ID}/reset`,
      {
        ...config,
        data: {
          user_ids: [999999, 999998]
        }
      }
    );
    console.log('❌ Should have failed for non-assigned users');
  } catch (error: any) {
    if (error.response && error.response.status === 400) {
      console.log('✅ Correctly handled non-assigned users');
    } else {
      console.log('❌ Unexpected error for non-assigned users:', error.response?.data);
    }
  }
}

async function runTests() {
  console.log('🚀 Starting Recipe View Statistics API Tests');
  console.log('Base URL:', BASE_URL);
  console.log('Test Recipe ID:', TEST_RECIPE_ID);
  console.log('Test User IDs:', TEST_USER_IDS);
  
  // Check if configuration is set
  if (AUTH_TOKEN === 'your-jwt-token-here') {
    console.log('❌ Please update AUTH_TOKEN in the script with a valid JWT token');
    return;
  }
  
  await testGetRecipeViewStatistics();
  await testResetRecipeViewStatisticsSpecificUsers();
  await testGetRecipeViewStatistics(); // Check if reset worked
  await testResetRecipeViewStatisticsAllUsers();
  await testGetRecipeViewStatistics(); // Check if reset worked
  await testErrorCases();
  
  console.log('\n🏁 Tests completed');
}

// Run the tests
runTests().catch(console.error);
