# Manual Testing Guide for Recipe View Statistics APIs

This directory contains manual test scripts for the new Recipe View Statistics APIs.

## New APIs Implemented

### 1. Get Recipe View Statistics
- **Endpoint**: `GET /api/v1/private/analytics/recipe-view-statistics/:recipeId`
- **Purpose**: Get view statistics for private recipes with assigned users
- **Response**: Returns detailed statistics including unique viewers, total views, and per-user view details

### 2. Reset Recipe View Statistics
- **Endpoint**: `DELETE /api/v1/private/analytics/recipe-view-statistics/:recipeId/reset`
- **Purpose**: Reset view statistics for private recipes with assigned users
- **Options**: 
  - Reset for specific users (provide `user_ids` array in request body)
  - Reset all statistics (omit `user_ids` from request body)

## Prerequisites for Testing

1. **Server Running**: Make sure the backend server is running on `http://localhost:3000`

2. **Database Setup**: Ensure your database has:
   - At least one private recipe (`has_recipe_private_visibility = true`)
   - The recipe must have assigned users in `mo_recipe_user` table with `status = 'active'`
   - Some analytics data in `mo_recipe_analytics` table with `event_type = 'recipe_view'`

3. **Authentication**: You need a valid JWT token for API access

## Test Data Setup

### Create Test Recipe (SQL)
```sql
-- Insert a private recipe
INSERT INTO mo_recipe (recipe_title, has_recipe_private_visibility, recipe_status, organization_id) 
VALUES ('Test Private Recipe', true, 'active', 'your-org-id');

-- Get the recipe ID
SET @recipe_id = LAST_INSERT_ID();

-- Assign users to the recipe
INSERT INTO mo_recipe_user (recipe_id, user_id, status, organization_id) 
VALUES 
  (@recipe_id, 1, 'active', 'your-org-id'),
  (@recipe_id, 2, 'active', 'your-org-id'),
  (@recipe_id, 3, 'active', 'your-org-id');

-- Add some analytics data
INSERT INTO mo_recipe_analytics (user_id, entity_type, entity_id, event_type, organization_id, created_at) 
VALUES 
  (1, 'recipe', @recipe_id, 'recipe_view', 'your-org-id', NOW()),
  (1, 'recipe', @recipe_id, 'recipe_view', 'your-org-id', NOW() - INTERVAL 1 DAY),
  (2, 'recipe', @recipe_id, 'recipe_view', 'your-org-id', NOW() - INTERVAL 2 HOUR),
  (3, 'recipe', @recipe_id, 'recipe_view', 'your-org-id', NOW() - INTERVAL 3 HOUR);
```

## Running the Tests

1. **Update Configuration**: Edit `recipe-view-statistics-test.ts` and update:
   - `TEST_RECIPE_ID`: Use the ID of your test private recipe
   - `TEST_USER_IDS`: Use the IDs of users assigned to the recipe
   - `AUTH_TOKEN`: Use a valid JWT token

2. **Run the Test Script**:
   ```bash
   cd backend-recipes-ms
   npx ts-node tests/manual/recipe-view-statistics-test.ts
   ```

## Manual Testing with Postman/cURL

### Get Recipe View Statistics
```bash
curl -X GET \
  "http://localhost:3000/api/v1/private/analytics/recipe-view-statistics/1" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### Reset Statistics for Specific Users
```bash
curl -X DELETE \
  "http://localhost:3000/api/v1/private/analytics/recipe-view-statistics/1/reset" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"user_ids": [1, 2]}'
```

### Reset All Statistics
```bash
curl -X DELETE \
  "http://localhost:3000/api/v1/private/analytics/recipe-view-statistics/1/reset" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

## Expected Responses

### Get Statistics Success Response
```json
{
  "status": true,
  "message": "Recipe view statistics retrieved successfully",
  "data": {
    "recipe_id": 1,
    "recipe_title": "Test Private Recipe",
    "assigned_users_count": 3,
    "statistics": {
      "unique_viewers": 2,
      "total_views": 4,
      "last_viewed_at": "2024-01-15T10:30:00Z",
      "avg_view_duration": null
    },
    "user_views": [
      {
        "user_id": 1,
        "user_email": "<EMAIL>",
        "view_count": 2,
        "first_viewed_at": "2024-01-14T10:30:00Z",
        "last_viewed_at": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

### Reset Statistics Success Response
```json
{
  "status": true,
  "message": "Recipe view statistics reset successfully for specified users"
}
```

## Error Cases to Test

1. **Non-existent recipe**: Should return 400 with "Recipe not found"
2. **Public recipe**: Should return 400 with "Recipe view statistics are only available for private recipes"
3. **Recipe without assigned users**: Should return 400 with "Recipe must have assigned users"
4. **Invalid user IDs in reset**: Should return 400 with "None of the specified users are assigned to this recipe"
5. **Invalid recipe ID format**: Should return 400 with "Invalid recipe ID"

## Troubleshooting

- **401 Unauthorized**: Check your JWT token is valid and not expired
- **404 Not Found**: Verify the API routes are properly registered
- **500 Internal Server Error**: Check server logs for database connection or query issues
- **No data returned**: Verify test data exists in the database tables
